#!/bin/sh
#
# ip-down.ipv6to4
#
#
# Taken from:
# (P) & (C) 2000-2005 by <PERSON> <<EMAIL>>
#
#  You will find more information on the initscripts-ipv6 homepage at
#   http://www.deepspace6.net/projects/initscripts-ipv6.html
#
# Version 2005-09-22
#
# Calling parameters:
#  $1: interface name
#
# Called (mostly) by /etc/ppp/ip-down.local
#  like: /etc/ppp/ip-down.ipv6to4 $1 >>/var/log/ppp-ipv6to4.log 2>&1
#
# Note: this script will *check* whether the existing 6to4 tunnel
#        was set before by using "ip-up.ipv6to4" comparing IPv4 address
#        of device with the generated 6to4 prefix
#
# Uses following information from /etc/sysconfig/network-scripts/ifcfg-$1:
#  IPV6TO4INIT=yes|no: controls configuration
#  IPV6TO4_ROUTING="<device>-<suffix>/<prefix length> ...": information to setup additional interfaces
#
#  IPV6_CONTROL_RADVD=yes|no: controls radvd triggering
#  IPV6_RADVD_PIDFILE=<file>: PID file of radvd for sending signals, default is "/var/run/radvd/radvd.pid"
#  IPV6_RADVD_TRIGGER_ACTION=startstop|reload|restart|SIGHUP: how to trigger radvd (optional, default is SIGHUP)
#


if [ -z "$1" ]; then
  	echo $"Argument 1 is empty but should contain interface name - skip IPv6to4 initialization"
	exit 1
fi

# Get global network configuration
. /etc/sysconfig/network

# Source IPv4 helper functions
cd /etc/sysconfig/network-scripts
. ./network-functions

CONFIG=$1
[ -f "$CONFIG" ] || CONFIG=ifcfg-$CONFIG
source_config

# IPv6 don't need aliases anymore, config is skipped
REALDEVICE=${DEVICE%%:*}
[ "$DEVICE" != "$REALDEVICE" ] && exit 0

if [ ! -f /etc/sysconfig/network-scripts/network-functions-ipv6 ]; then
	exit 1
fi

. /etc/sysconfig/network-scripts/network-functions-ipv6


# Run basic IPv6 test, if not ok, skip IPv6 initialization
ipv6_test testonly || exit 0

# Test status of ppp device
ipv6_test_device_status $DEVICE
if [ $? != 0 -a $? != 11 ]; then
        # device doesn't exist or other problem occurs
        exit 1
fi

# Test status of tun6to4 device
ipv6_test_device_status tun6to4
if [ $? = 0 -o $? = 11 ]; then
	# Device exists
	valid6to4config="yes"

	# Get IPv4 address from interface
	ipv4addr="$(ipv6_get_ipv4addr_of_device $DEVICE)"
	if [ -z "$ipv4addr" ]; then
		# Has no IPv4 address
		valid6to4config="no"
	fi

	# Get local IPv4 address of dedicated tunnel
	ipv4addr6to4local="$(ipv6_get_ipv4addr_of_tunnel tun6to4 local)"

	# IPv6to4 not enabled on this interface?
	if [ $IPV6TO4INIT != "yes" ]; then
		# Check against configured 6to4 tunnel to see if this interface was regardless used before
		if [ "$ipv4addr" != "$ipv4addr6to4local" ]; then
			# IPv4 address of interface does't match local tunnel address, interface was not used for current 6to4 setup
			valid6to4config="no"
		fi
	fi

fi

if [ "$valid6to4config" = "yes" ]; then
	if [ "$IPV6_CONTROL_RADVD" = "yes" ]; then
		# Control running radvd
		ipv6_trigger_radvd down "$IPV6_RADVD_TRIGGER_ACTION" $IPV6_RADVD_PIDFILE
	fi

	if [ -n "$IPV6TO4_ROUTING" ]; then
		# Delete routes to local networks
		for devsuf in $IPV6TO4_ROUTING; do
			dev="${devsuf%%-*}"
			ipv6_cleanup_6to4_device $dev
		done
	fi

	# Delete all configured 6to4 address
	ipv6_cleanup_6to4_tunnels tun6to4
fi
