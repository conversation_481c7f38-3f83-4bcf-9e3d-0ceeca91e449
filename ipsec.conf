version 2.0
config setup
  ikev1-policy=accept
  virtual-private=%v4:10.0.0.0/8,%v4:192.168.0.0/16,%v4:172.16.0.0/12,%v4:!192.168.42.0/24,%v4:!192.168.43.0/24
  uniqueids=no
  
conn wg-tunnel
    auto=ignore
    left=10.199.10.2
    leftid=10.199.10.2
    leftprotoport=17/1701
	right=%any
    rightprotoport=17/%any
	modecfgdns="******* *******"
    type=transport
	phase2=esp
    encapsulation=yes
    authby=secret
    pfs=no
    rekey=no
    keyingtries=5
    dpddelay=30
    dpdtimeout=300
    dpdaction=clear
    ikev2=never
    ike=aes256-sha1;modp2048
    phase2alg=aes256-sha1
    ikelifetime=24h
    salifetime=24h
    sha2-truncbug=yes

conn shared
	left=%defaultroute
	leftid=@server
	right=%any
	encapsulation=yes
	authby=secret
	pfs=no
	rekey=no
	keyingtries=5
	dpddelay=30
	dpdtimeout=300
	dpdaction=clear
	ikev2=never
	ike=aes256-sha1;modp2048
	phase2alg=aes256-sha1
	ikelifetime=24h
	salifetime=24h
	sha2-truncbug=yes  

conn l2tp-psk
	auto=add
	leftprotoport=17/1701
	rightprotoport=17/%any
	type=transport
	modecfgdns="******* *******"
	phase2=esp
	also=shared

conn xauth-psk
	auto=add
	leftsubnet=0.0.0.0/0
	rightaddresspool=***********-************
	modecfgdns="******* *******"
	leftxauthserver=yes
	rightxauthclient=yes
	leftmodecfgserver=yes
	rightmodecfgclient=yes
	modecfgpull=yes
	cisco-unity=yes
	also=shared
	
