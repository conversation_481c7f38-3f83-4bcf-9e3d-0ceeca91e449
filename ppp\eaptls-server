# Parameters for authentication using EAP-TLS (server)

# client name (can be *)
# server name (can be *)
# client certificate file (optional, if unused put '-')
# server certificate file (required)
# CA certificate file (required)
# server private key file (required)
# allowed addresses (required, can be *)

#client	server	-	/root/cert/server.crt	/root/cert/ca.crt	/root/cert/server.key	***********/24
