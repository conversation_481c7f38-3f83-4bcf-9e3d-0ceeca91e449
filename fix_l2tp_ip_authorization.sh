#!/bin/bash
# L2TP IP授权问题修复脚本
# 解决 "<PERSON><PERSON> is not authorized to use remote address" 错误

# 日志记录函数
log() {
    echo -e "\033[32m[$(date '+%Y-%m-%d %H:%M:%S')] $1\033[0m"
    logger -t "L2TP-IP-FIX" "$1"
}

error() {
    echo -e "\033[31m[$(date '+%Y-%m-%d %H:%M:%S')] 错误: $1\033[0m"
    logger -t "L2TP-IP-FIX-ERROR" "$1"
    exit 1
}

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    error "必须使用root权限运行此脚本"
fi

log "开始修复L2TP IP授权问题..."

# 1. 备份原始配置文件
BACKUP_DIR="/etc/backup/l2tp_$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR
log "创建备份目录: $BACKUP_DIR"

# 备份现有配置
if [ -f /etc/ipsec.conf ]; then
    cp /etc/ipsec.conf $BACKUP_DIR/
    log "已备份 /etc/ipsec.conf"
fi

if [ -f /etc/xl2tpd/xl2tpd.conf ]; then
    cp /etc/xl2tpd/xl2tpd.conf $BACKUP_DIR/
    log "已备份 /etc/xl2tpd/xl2tpd.conf"
fi

if [ -f /etc/ppp/options.xl2tpd ]; then
    cp /etc/ppp/options.xl2tpd $BACKUP_DIR/
    log "已备份 /etc/ppp/options.xl2tpd"
fi

# 2. 复制修复后的配置文件
log "复制修复后的配置文件..."

# 复制ipsec.conf
cp ipsec.conf /etc/ipsec.conf
chmod 644 /etc/ipsec.conf
log "已更新 /etc/ipsec.conf"

# 复制xl2tpd.conf
mkdir -p /etc/xl2tpd
cp xl2tpd.conf /etc/xl2tpd/xl2tpd.conf
chmod 644 /etc/xl2tpd/xl2tpd.conf
log "已更新 /etc/xl2tpd/xl2tpd.conf"

# 复制options.xl2tpd
mkdir -p /etc/ppp
cp options.xl2tpd /etc/ppp/options.xl2tpd
chmod 644 /etc/ppp/options.xl2tpd
log "已更新 /etc/ppp/options.xl2tpd"

# 3. 复制PPP脚本
cp ppp-ip-up /etc/ppp/ip-up
cp ppp-ip-down /etc/ppp/ip-down
chmod 755 /etc/ppp/ip-up
chmod 755 /etc/ppp/ip-down
log "已安装PPP脚本"

# 4. 复制pppd头文件（如果不存在）
if [ ! -d /usr/include/pppd ]; then
    mkdir -p /usr/include/pppd
    cp -r pppd/* /usr/include/pppd/
    chmod -R 644 /usr/include/pppd/*
    log "已安装pppd头文件"
fi

# 5. 验证配置
log "验证配置文件..."

# 检查ipsec配置
ipsec verify 2>/dev/null | grep -q "OK" && log "IPSec配置验证通过" || log "警告: IPSec配置可能有问题"

# 检查xl2tpd配置
if xl2tpd -D 2>&1 | grep -q "xl2tpd version"; then
    log "xl2tpd配置语法正确"
else
    log "警告: xl2tpd配置可能有语法错误"
fi

# 6. 重启服务
log "重启相关服务..."

systemctl restart strongswan 2>/dev/null || systemctl restart ipsec 2>/dev/null
if [ $? -eq 0 ]; then
    log "IPSec服务重启成功"
else
    log "警告: IPSec服务重启失败"
fi

systemctl restart xl2tpd
if [ $? -eq 0 ]; then
    log "xl2tpd服务重启成功"
else
    error "xl2tpd服务重启失败"
fi

# 7. 检查服务状态
log "检查服务状态..."
systemctl is-active xl2tpd >/dev/null && log "xl2tpd服务运行正常" || log "警告: xl2tpd服务未运行"
systemctl is-active strongswan >/dev/null && log "strongswan服务运行正常" || systemctl is-active ipsec >/dev/null && log "ipsec服务运行正常" || log "警告: IPSec服务未运行"

# 8. 显示修复摘要
log "修复完成！"
echo ""
echo -e "\033[32m=== L2TP IP授权问题修复摘要 ===\033[0m"
echo "✅ 修改了IP地址分配范围: ***********-************"
echo "✅ 添加了PPP IP授权配置"
echo "✅ 安装了PPP连接脚本"
echo "✅ 更新了相关配置文件"
echo "✅ 重启了相关服务"
echo ""
echo -e "\033[33m主要修复内容:\033[0m"
echo "1. 避免使用 *********** 地址（改为从 .20 开始）"
echo "2. 添加了 noipdefault 和 ipcp 配置"
echo "3. 安装了 ip-up/ip-down 脚本进行IP授权"
echo "4. 同步了 ipsec.conf 和 xl2tpd.conf 的IP范围"
echo ""
echo -e "\033[33m测试建议:\033[0m"
echo "1. 使用iOS设备重新连接L2TP VPN"
echo "2. 监控日志: tail -f /var/log/messages | grep -E '(ppp|l2tp|ipsec)'"
echo "3. 检查分配的IP地址是否在新范围内"
echo ""
echo -e "\033[31m备份位置:\033[0m $BACKUP_DIR"

log "修复脚本执行完成"
