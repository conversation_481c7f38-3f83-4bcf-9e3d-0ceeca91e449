#!/bin/sh
#
# ipv6-down
#
# Called by pppd after IPV6CP/down was finished
#
# This file should not be modified -- make local changes to
# /etc/ppp/ipv6-down.local instead
#
#
# Taken from:
# (P) & (C) 2001-2006 by <PERSON> <<EMAIL>>
#
#  You will find more information on the initscripts-ipv6 homepage at
#   http://www.deepspace6.net/projects/initscripts-ipv6.html
#
# RHL integration assistance by <PERSON><PERSON><PERSON> <<EMAIL>>
#
# Calling parameters:
#  $1: interface name
#  $6: logical interface name (set by pppd option ipparam)
#
# Version 2006-08-02
#
# Uses following information from /etc/sysconfig/network-scripts/ifcfg-$1:
#  IPV6INIT=yes|no: controls IPv6 configuration for this interface
#


PATH=/sbin:/usr/sbin:/bin:/usr/bin
export PATH

LOGDEVICE=$6
REALDEVICE=$1

[ -f /etc/sysconfig/network ] || exit 0
. /etc/sysconfig/network

cd /etc/sysconfig/network-scripts
. ./network-functions

CONFIG=$LOGDEVICE
[ -f "$CONFIG" ] || CONFIG=ifcfg-$CONFIG
source_config

[ -f /etc/sysconfig/network-scripts/network-functions-ipv6 ] || exit 1
. /etc/sysconfig/network-scripts/network-functions-ipv6

[ -x /etc/ppp/ipv6-down.local ] && /etc/ppp/ipv6-down.local "$@"


if [ "$IPV6_CONTROL_RADVD" = "yes" ]; then
	# Control running radvd
	ipv6_trigger_radvd down "$IPV6_RADVD_TRIGGER_ACTION" $IPV6_RADVD_PIDFILE
fi

# IPv6 test, no module loaded, exit if system is not IPv6-ready
ipv6_test testonly || exit 0

# Test device status
ipv6_test_device_status $REALDEVICE
if [ $? != 0 -a $? != 11 ]; then
	# device doesn't exist or other problem occurs
	exit 1
fi

# Delete all current configured IPv6 addresses on this interface
ipv6_cleanup_device $REALDEVICE

exit 0
